$(document).ready(function () {
  let checkCamera
  pippletHelper.trackAnalytics('identify_navigator')

  const ExtensionsCheckInterposed = function () {
    const $extensionsCheck = $('#extensions_check')

    if ($extensionsCheck.hasClass('display')) {
      $('#test_onboarding_security_guidelines').hide()
      $extensionsCheck.show().removeClass('display')
      return true
    }
    return false
  }

  $('#start_security_guidelines').click(function () {
    $('#test_tips_to_succeed').hide()
    $('#test_onboarding_security_guidelines').show()
    $('#camera_check').hide()
    $('#keyboard_test').hide()
    $('#please_authorise_mic').hide()
  })

  // -------------------------------------------------
  // Entry point: start mic check
  $('#start_audio_test, #start_audio_test_extensions').click(function () {
    $('#extensions_check').hide()
    Pipplet.TestOnboardingBreadCrumbs.transitionTo('hardware_check')
    if (ExtensionsCheckInterposed()) return

    $('#test_tips_to_succeed').hide()
    $('#test_onboarding_security_guidelines').hide()
    $('#camera_check').hide()
    $('#keyboard_test').hide()
    $('#please_authorise_mic').show()
  })

  $('#go_tips_to_succeed').click(function () {
    Pipplet.TestOnboardingBreadCrumbs.transitionTo('succeed_tips')
    // Useful for mobile devices
    window.scrollTo(0, 0)
    $('#test_introduction').hide()
    $('#test_tips_to_succeed').show()
  })

  // -------------------------------------------------
  // Guide user to authorise microphone
  // Setup Recorder for mic test once audio capture is activated
  $('#authorise_mic,#authorise_mic_again').click(function () {
    Pipplet.Audio.requestAudioCapture(
      // Callback if audio capture is accepted
      function () {
        $('#please_authorise_mic').hide()
        $('#audio_capture_refused').hide()
        $('#no_microphone_detected').hide()
        $('#audio-test-content').fadeIn()
        Pipplet.Test.saveTestData()
      },

      // Callback if audio capture is refused
      function () {
        $('#please_authorise_mic').hide()
        $('#audio_capture_refused').fadeIn()
        Pipplet.Test.saveTestData()
      },

      // Callback if no microphone is available
      function () {
        $('#please_authorise_mic').hide()
        $('#no_microphone_detected').fadeIn()
        Pipplet.Test.saveTestData()
      }

    )
  }) // #authorise_mic,#authorise_mic_again - Click handler

  // -------------------------------------------------
  // Record audio locally
  // Test recording volume automatically
  // Allow user to listen to recording
  // Display text to gather user feedback
  $('#mic_test_record_button').click(function () {
    // If the user clicks to start the recording
    if ($(this).attr('meta_action') == 'record') {
      $(this).attr('meta_action', 'disabled')

      $('#audio-elt').removeAttr('src')
      $('#bad_audio_alert').hide()
      $('#audio_player').hide()
      $('#audio_player_check').hide()
      $('#audio_player .recording_player').hide()
      $('#launch_test_button').hide()
      $('#play-button').hide()

      // Create new recording object and start recording
      const recording = new Pipplet.Recording('audio-test')
      recording.startRecording(
        function (recordingUrl) { // successCallback
          $('#mic_test_record_button').attr('meta_action', 'record')
          $('#audio-elt').attr('src', recordingUrl)
          $('#spinner').spin(true)
          Pipplet.Test.waitAssetsLoad(function () {
            $('#spinner').spin(false)
            $('#audio_player .recording_player').show()
            $('#audio_player_check').show()
          },
            function () {
              $('#spinner').spin(false)
              $('#audio_player').hide()
              $('#audio_download_failed').show()
            }
          )
          Pipplet.Test.saveTestData()
        },
        function () { // validationsFailedCallback
          $('#mic_test_record_button').attr('meta_action', 'record')
          $('#audio_player').hide()
          $('#bad_audio_alert').show()
          Pipplet.Test.saveTestData()
        },
        function () { // uploadFailedCallback
          pippletHelper.debug('[Pipplet.MicrophoneTest::#mic_test_record_button] Error while uploading audio to S3')
          $('#audio-test-content').hide()
          $('#audio-test-upload_to_s3_failed').show()
          Pipplet.Test.saveTestData()
        },
        function () { // errorCallback
          pippletHelper.debug('[Pipplet.MicrophoneTest::#mic_test_record_button] Something went wrong during the audio recording')
          $('#audio-test-content').hide()
          $('#audio-test-no_audio_device_available_or_allowed').show()
          Pipplet.Test.saveTestData()
        },
        function () { // afterStartCallback
          $('#mic_test_record_button').addClass('recording').attr('meta_action', 'stop')
        }
      ) // recording.startRecording

      Pipplet.Test.saveTestData()

      // If the user clicks to stop the recording
    } else if ($(this).attr('meta_action') == 'stop') {
      $(this).attr('meta_action', 'disabled')
      $(this).removeClass('recording')
      $('#audio_player').show()

      Pipplet.Recording.stop()
      Pipplet.Test.saveTestData()
    }
  }) // #mic_test_record_button - Click handler

  // -------------------------------------------------
  // Play audio and display text to gather user feedback
  $('#play-button').click(function () {
    // Manage audio player
    // If browser safari use lib to read ogg files
    if ($('#audio-elt')[0] && pippletHelper.needsOGVjs() && (typeof pippletOgvjsApplyAudioTestPlayer === 'function')) {
      var playPromise = pippletOgvjsApplyAudioTestPlayer()
    } else {
      var playPromise = document.querySelector('#audio-elt').play()
    }

    // In browsers that don’t yet support this functionality,
    // playPromise won’t be defined.
    if (playPromise !== undefined) {
      playPromise.then(function () {
        // Automatic playback started!
      }).catch(function (error) {
        // Automatic playback failed.
        pippletHelper.debug('[Pipplet.MicrophoneTest::#play-button] failed')
        pippletHelper.debug(error)
        // Show a UI element to let the user manually start playback.
      })
    }

    $('#audio_player_check').show()
    $('#launch_test_button').show()
  }) // #audio_player - Click handler

  // -------------------------------------------------
  // Update user status and redirect to test
  $('#start_tutorial, #start_tutorial_no_audio_test,  #start_tutorial_no_audio_test_extensions').click(function () {
    $('#extensions_check').hide()
    $('#test_tips_to_succeed').hide()
    $('#test_onboarding_security_guidelines').hide()
    if (ExtensionsCheckInterposed()) return

    stopCamera()
    $.ajax({
      type: 'POST',
      url: '/test_instances/audio_tested',
      success: function (data) {
        const skip_tutorial = $('meta[name=skip_tutorial]').attr('content')
        if (skip_tutorial === 'true') {
          window.location = '/questions'
        } else {
          // Start Tutorial !
          $('#test_onboarding_authorise_audio').remove()
          $('#test_onboarding_microphone_check').remove()
          $('#test_introduction').remove()
          $('#camera_check').remove()
          $('#test_onboarding_tutorial').show()
          Pipplet.Tutorial.load()
        }
      },
      error: function (status) {
        pippletHelper.debug('[Pipplet.MicrophoneTest::#start_tutorial][AJAX] error: ' + status)
      }
    })
  }) // #launch_test - Click handler

  // -------------------------------------------------
  // If camera check is needed, show special page
  $('#start_check_camera, #start_check_camera_extensions').click(function () {
    $('#extensions_check').hide()
    Pipplet.TestOnboardingBreadCrumbs.transitionTo('hardware_check')
    if (ExtensionsCheckInterposed()) return

    $('#test_tips_to_succeed').hide()
    $('#test_onboarding_security_guidelines').hide()
    $('#keyboard_test').hide()
    $('#camera_check').show()
    startCamera()
  }) // #launch_test - Click handler

  $('#start_keyboard_test, #start_keyboard_test_extensions').click(function () {
    $('#extensions_check').hide()
    Pipplet.TestOnboardingBreadCrumbs.transitionTo('hardware_check')
    if (ExtensionsCheckInterposed()) return

    $('#test_tips_to_succeed').hide()
    $('#test_onboarding_security_guidelines').hide()
    $('#keyboard_test').show()
  })

  const resetKeyboardValidations = function () {
    $('#valid_msg').hide()
    $('#error_msg').hide()
  }

  $('#keyboard_test_input').on('keyup', function () {
    resetKeyboardValidations()
    $('#valid_msg').hide()
    if ($('#keyboard_test_input').val() === $('#text_to_match').text()) {
      $('#valid_msg').show()
    }
  })

  $('#trigger_validations').on('click', function () {
    resetKeyboardValidations()
    $('#error_msg').hide()
    if ($('#keyboard_test_input').val() === $('#text_to_match').text()) {
      $('.toClick').click()
    } else {
      $('#error_msg').show()
    }
  })

  var startCamera = function () {
    // Check id?
    const id_checks = $('meta[name=test_instance_idc]').attr('content')

    // Load camera
    if (id_checks && window.JpegCamera) {
      let camera_lang = $('meta[name=camera_lang]').attr('content')
      if (camera_lang == undefined) {
        camera_lang = 'en'
      }

      checkCamera = new Pipplet.Camera({
        shutter: false,
        /* set true to hear snapshots shutter */
        snapshots: {
          interval: 3000 /*  Snapshots Interval */
        },
        message: {
          lang: camera_lang /* Set language messages */
        },
        AWS: {},
        /* Nothing recorded during webcam test */
        testid: $('meta[name=test_instance_id]').attr('content') || 123
      })

      checkCamera.test()
    }
  }

  var stopCamera = function () {
    if (checkCamera !== undefined) {
      checkCamera.stop()
    }
  }
}) // document.ready
