# == Schema Information
#
# Table name: test_instances
#
#  id                                  :integer          not null, primary key
#  anonymized_at                       :datetime
#  begin_date                          :datetime
#  cached_count_of_questions_answered  :integer
#  cached_count_of_questions_to_answer :integer
#  certificate_sent_to_candidate       :boolean
#  client_contact_email                :string
#  client_contact_first_name           :string
#  client_contact_last_name            :string
#  client_contact_locale               :string
#  client_name                         :string
#  client_type                         :string
#  count_of_reminders_sent             :integer          default(0)
#  count_of_sms_reminders              :integer          default(0)
#  count_of_tekis_reminders            :integer          default(0)
#  deleted_at                          :datetime
#  duplicate                           :string
#  end_date                            :datetime
#  graded_at                           :datetime
#  had_check_failed                    :boolean          default(FALSE)
#  last_reminder_sent_at               :datetime
#  last_sms_reminder_sent_at           :datetime
#  last_tekis_email_send_at            :datetime
#  monitored                           :boolean          default(FALSE)
#  redirection_url                     :string
#  regrade_requested_at                :datetime
#  report_content_type                 :string
#  report_file_name                    :string
#  report_file_size                    :integer
#  report_updated_at                   :datetime
#  result_rating                       :integer
#  review_required                     :boolean          default(FALSE)
#  review_required_modified_at         :datetime
#  sanitized_client_name               :text
#  send_reminders                      :boolean          default(TRUE)
#  sent_for_evaluation_date            :datetime
#  skills_tested                       :string           default([]), is an Array
#  skip_audio_test                     :boolean          default(FALSE)
#  skip_browser_check                  :boolean          default(FALSE)
#  skip_email                          :boolean          default(FALSE)
#  skip_identity_check                 :boolean          default(TRUE)
#  skip_tutorial                       :boolean          default(FALSE)
#  sms_reminders                       :boolean          default(FALSE)
#  source_type                         :string
#  status                              :string           default("initialized"), not null
#  test_language                       :string
#  test_mode                           :boolean          default(TRUE)
#  time_multiplier                     :float            default(1.0)
#  uuid                                :string           not null
#  wheebox_face_training_completed     :boolean
#  created_at                          :datetime         not null
#  updated_at                          :datetime         not null
#  api_user_id                         :integer
#  client_id                           :integer
#  delivered_evaluation_id             :integer
#  direct_user_id                      :integer
#  evaluation_id                       :integer
#  examiner_id                         :integer
#  pipplet_clients_campaign_id         :integer
#  question_set_id                     :integer
#  test_profile_id                     :integer
#  user_id                             :integer
#  wheebox_attempt_id                  :integer
#
# Indexes
#
#  index_test_instances_on_api_user_id            (api_user_id)
#  index_test_instances_on_client_name            (client_name)
#  index_test_instances_on_question_set_id        (question_set_id)
#  index_test_instances_on_sanitized_client_name  (sanitized_client_name)
#  index_test_instances_on_status                 (status)
#  index_test_instances_on_test_language          (test_language)
#  index_test_instances_on_test_mode              (test_mode)
#  index_test_instances_on_test_profile_id        (test_profile_id)
#  index_test_instances_on_user_id                (user_id)
#  index_test_instances_on_uuid                   (uuid)
#  test_instances_composite_index_deleted_graded  (graded_at) WHERE (deleted_at IS NULL)
#

class TestInstance < ApplicationRecord
  self.ignored_columns += [
    :cut_min_limit,
    :cut_max_limit
  ]

  # ----------------------------------------
  # Sections in this file
  #     * Associations
  #     * PDF report attachment + Scores
  #     * Validations
  #     * Statuses
  #     * Scopes
  #     * Callbacks
  #     * Instance methods
  #     * Sources
  #     * Audit trail
  #     * Test validation & Evaluation
  #     * Email reminders
  #     * Accessors & stats about answered questions
  #     * Create USER & TEST_INSTANCE
  # ----------------------------------------

  # Soft delete object
  acts_as_paranoid

  # Next question logic
  include NextQuestionableLogic
  include ApplicationHelper

  include Identity::TestInstance

  # Can be tagged and untaggued
  include Taggable
  include Anonymizable

  include AfterCommitWhenAttributesChange
  after_commit_when_attributes_change [:report_file_name] do |test_instance|
    PippletClientsApi.request_user_sync(test_instance_uuid: test_instance.uuid) if test_instance&.client_id
  end

  FINISHED_STATUSES = %i[completed validations_passed validations_failed sent_for_evaluation graded].freeze
  NEED_VALIDATION = %i[initialized ready connected audio_tested tutored in_progress].freeze
  NOT_STARTED = %i[initialized ready connected audio_tested tutored].freeze
  NON_CANCELLABLE_STATUSES = (FINISHED_STATUSES + %i[cancelled]).freeze
  CANCELLABLE_STATUSES = %i[initialized ready connected audio_tested tutored in_progress pending].freeze
  MAX_SMS_REMINDERS = 1

  attr_writer :skip_cancel_notification

  # ----------------------------------------
  # :section: Associations
  # ----------------------------------------
  belongs_to :user
  belongs_to :test_profile
  belongs_to :api_user
  belongs_to :direct_user

  belongs_to :examiner
  belongs_to :client_config, -> { where(client_configs: { active: true }).where.not(client_configs: { sanitized_client_name: [nil, ''] }).limit(1) }, class_name: 'ClientConfig', foreign_key: 'sanitized_client_name', :primary_key => 'sanitized_client_name'

  has_one :test_instance_validation_detail
  has_one :test_instance_validation, through: :test_profile
  has_one :api_order
  has_one :certificate, dependent: :destroy
  belongs_to :delivered_evaluation, class_name: 'Evaluation', foreign_key: 'delivered_evaluation_id'

  has_many :productions, dependent: :destroy
  has_many :receptions, dependent: :destroy
  has_many :results, dependent: :destroy
  has_many :evaluations, -> { order(created_at: :asc) }, dependent: :destroy # Keep the order to avoid sorting problem. See https://github.com/pipplet/pipplet/pull/1508
  has_many :event_logs, as: :loggable
  has_many :behaviour_logs, as: :loggable
  has_many :images, as: :attachable
  has_many :average_grades, as: :average_gradable
  has_many :technical_issues, dependent: :destroy
  has_many :dynamic_question_data_from_productions, through: :productions, foreign_key: 'dqd_able_id', source: :dynamic_question_data, class_name: 'DynamicQuestionDatum'
  has_many :challenges, through: :receptions

  # ----------------------------------------
  # :section: PDF report attachment + Scores
  # ----------------------------------------
  has_attached_file :report, {
    s3_protocol: :https,
    path: '/:class/:style/:hashed_name.:extension',
    hash_secret: 'qT60JCzayxmLwF7lwFRJRmDtD15n7LQcOpMr5AMENFxJSr5geOz10FieVk0xXr3V'
  }
  validates_attachment_content_type :report, content_type: ['application/pdf']

  # ----------------------------------------
  # :section: Scopes
  # ----------------------------------------

  scope :graded_between, ->(start_date, end_date) { graded.where(graded_at: start_date..end_date) }
  scope :with_redos, lambda {
    joins(:productions)
      .having(
        Arel.sql(
          "COUNT(*) FILTER(WHERE(productions.status = #{Production.statuses[:to_be_redone]})) > 0"
        )
      )
      .group(:id)
  }
  scope :with_evaluations_with_s_eval_tag, lambda {
    joins(evaluations: :tags)
      .where(tags: { name: 's-eval' })
      .distinct
  }
  scope :with_tekis, -> { joins(:technical_issues).distinct }
  scope :skip_identity_false, -> { where(skip_identity_check: false) }

  scope :real_with_sms_reminders, lambda {
    not_started.where(test_mode: false,
                      created_at: ..2.days.ago,
                      sms_reminders: true,
                      count_of_sms_reminders: 0)
  }

  scope :real, -> { where(test_mode: false) }

  scope :training, lambda {
    joins(:test_profile)
      .where(test_profile: { client_type: TestProfile.client_types['training'] })
      .or(TestInstance.where(source_type: TestProfile.client_types['training']))
  }

  scope :recruitment, lambda {
    joins(:test_profile)
      .where(test_profile: { client_type: TestProfile.client_types['recruitment'] })
      .or(TestInstance.where(source_type: TestProfile.client_types['recruitment']))
  }

  scope :not_started, -> { where({ status: %i[initialized ready connected] }) }

  scope :started, -> { where.not({ status: %i[initialized ready connected] }) }

  scope :completed, lambda {
    where({ status: %i[cancelled completed graded validations_failed validations_passed] })
  }

  scope :not_completed, lambda {
    where.not({ status: %i[cancelled completed graded validations_failed validations_passed pending sent_for_evaluation] })
  }

  scope :waiting_validation, -> { where({ test_mode: false, status: %i[completed] }) }

  scope :waiting_grade, lambda {
    where({ test_mode: false, status: %i[completed validations_passed sent_for_evaluation] })
  }

  scope :last_update_22h_ago, -> { where("test_instances.updated_at < '#{Time.now - (22 * 60 * 60)}'") }

  scope :not_cancelled, -> { where.not({ status: [:cancelled] }) }

  scope :not_graded, -> { where.not({ status: [:graded] }) }

  scope :not_test_mode, -> { where({ test_mode: false }) }

  scope :test_mode, -> { where({ test_mode: true }) }

  scope :not_review_required, -> { where({ review_required: false }) }

  scope :review_required, -> { where({ review_required: true }) }

  scope :review_required_not_modified, -> { where({ review_required_modified_at: nil }) }

  scope :review_required_modified, -> { where.not({ review_required_modified_at: nil }) }

  scope :end_more_than_24_hours_ago, lambda {
    where("test_instances.end_date <  '#{24.business_hour.before(Time.now)}'")
  }
  scope :reviewed_more_than_24_hours_ago, lambda {
    where("review_required_modified_at < '#{24.business_hour.before(Time.now)}'")
  }
  scope :ended, -> { where.not({ end_date: nil }) }

  scope :monitored, -> { where({ monitored: true }) }

  scope :no_next_questionables, -> { left_joins(:next_questionables).having('COUNT(next_questionables.id) = 0').group('test_instances.id') }

  scope :available_for_tekis_reminder, lambda {
    joins(:technical_issues)
      .where(test_mode: false,
             count_of_tekis_reminders: 0..TechnicalIssue::MAX_TEKI_REMINDERS,
             technical_issues: { status: %w[ongoing pending] })
      .where('last_tekis_email_send_at IS NULL OR last_tekis_email_send_at < ?', 24.hours.ago)
      .where(created_at: ..24.hours.ago)
  }

  scope :overdue_for_tekis_reminder, lambda {
    joins(:technical_issues)
      .where(test_mode: false,
             count_of_tekis_reminders: (TechnicalIssue::MAX_TEKI_REMINDERS + 1)..,
             last_tekis_email_send_at: 1.month.ago..3.days.ago,
             technical_issues: { status: %w[ongoing pending] })
  }

  scope :available_for_reminder, lambda {
    where.not(test_instances: { status: %i[graded completed validations_failed validations_passed] })
         .where(test_instances: { send_reminders: true, test_mode: false })
         .where(count_of_tekis_reminders: 0)
         .where(test_instances: { created_at: ..24.hours.ago })
         .where(test_instances: { count_of_reminders_sent: ...MAX_NUMBER_OF_REMINDERS_PER_TEST_INSTANCE })
         .where('test_instances.created_at <= ? AND (test_instances.last_reminder_sent_at IS NULL OR test_instances.last_reminder_sent_at <= ?)', DELAY_IN_DAYS_BETWEEN_EACH_REMINDER.days.ago, DELAY_IN_DAYS_BETWEEN_EACH_REMINDER.days.ago)
         .where.not(test_instances: { client_name: ClientConfig.with_no_reminders.select(:client_name) })
  }

  # Where technical issue is ongoing
  scope :with_ongoing_technical_issues, -> { joins(:technical_issues).where(technical_issues: { status: :ongoing }) }

  scope :need_validation, -> { where(status: NEED_VALIDATION) }

  scope :anonymized_or_user_anonymized, -> { joins(:user).anonymized.or(User.anonymized) }
  scope :non_anonymized_and_user_non_anonymized, -> { joins(:user).non_anonymized.where(user: { anonymized_at: nil }) }
  scope :skip_identity_started, -> { skip_identity_false.started.not_graded }
  scope :finished, -> { where(status: FINISHED_STATUSES) }

  def browser_details_log
    event_logs.where(category: 'browser_details')
  end

  def self.report_hashed_name_secret
    'qT60JCzayxmLwF7lwFRJRmDtD15n7LQcOpMr5AMENFxJSr5geOz10FieVk0xXr3V'
  end

  def test_language_sym
    @test_language_sym = nil if previous_changes.key?(:test_language)
    @test_language_sym ||= test_language.to_sym
  end

  def client_formated_status
    # TestInstance States
    # state :initialized, :initial => true  # Object has been created
    # state :ready                          # Test is ready to be answered by the user
    # state :connected                      # User connected to the interface for this test
    # state :audio_tested                   # User tested audio
    # state :tutored                        # User viewed the tutorial
    # state :in_progress                    # User started answering questions
    # state :completed                      # User answered all questions
    # state :validations_failed             # All questions answered, but failed automatic validations
    # state :validations_passed             # All questions answered and passed automatic validations
    # state :graded                         # Evaluation completed, score received
    case status
    when 'initialized', 'ready', 'connected', 'audio_tested', 'tutored' then 'initialized'
    when 'in_progress', 'validations_failed', 'completed', 'validations_passed', 'pending' then 'ongoing'
    when 'cancelled' then 'cancelled'
    when 'sent_for_evaluation' then 'completed'
    when 'graded' then 'graded'
    when 'deleted' then 'inactive'
    else
      'unknown'
    end
  end

  # Specific method to create a hashed filename and that can also be called from the controller when uploading to S3
  def hashed_name
    if Rails.env.production?
      Digest::MD5.hexdigest("--#{self.class.name}--#{id}-- #{self.class.report_hashed_name_secret} audition--")
    else
      Digest::MD5.hexdigest("--#{self.class.name}--#{id}-- #{self.class.report_hashed_name_secret} #{Rails.env}--")
    end
  end

  def need_speaking?
    skills_tested.blank? || skills_tested&.include?('speaking')
  end

  def speaking_only?
    skills_tested&.include?('speaking') && !skills_tested&.include?('writing')
  end

  def writing_only?
    skills_tested&.include?('writing') && !skills_tested&.include?('speaking')
  end

  def keyboard_check?
    return false if (test_language == 'ja' && [182, 94].include?(test_profile_id)) || (test_language == 'zhcn' && [97, 139].include?(test_profile_id)) || speaking_only?

    KEYBOARD_CHECK_LANGUAGE.key?(test_language)
  end

  def has_valid_report?
    report&.url && !report_file_name.nil?
  end

  def certificate_url
    Rails.application.routes.url_helpers.certificate_url(id: uuid)
  end

  def result_rating_to_cecrl
    result_rating.present? ? get_cecrl_score(result_rating) : nil
  end

  def average_grade_of_trusted_examiners
    grade = average_grades.find_by(label: 'overall')
    if grade.blank?
      nil
    else
      "#{grade.cecrl_score} | #{grade.score}"
    end
  end

  def average_result_ratings_of_evaluations
    evals = evaluations.delivered + evaluations.assessed

    evals -= Evaluation.where(examiner: Examiner.pipplet_admin_examiner(test_language))

    total_rating = 0
    total_evaluations = 0
    average_rating = nil
    evals.each do |e|
      examiner = e.examiner

      if examiner.status == 'trusted' && !e.certificate.nil? && !e.certificate.grades.empty? && !e.certificate.grades.overall.empty?
        total_rating += e.certificate.grades.overall.last.score
        total_evaluations += 1
      end
    end

    total_evaluations = 1 if total_evaluations == 0

    get_cecrl_score(total_rating.to_f / total_evaluations)

    
  end

  def delete_all_redo
    next_questionables.each do |nxqt|
      EventLog.add(nxqt.test_instance, :admin,
                   "ADMIN#delete_all_redo_production! production=#{nxqt.id} challenge=#{nxqt.challenge_id}", :info)
      nxqt.destroy!
    end
    update_next_questionables
  end

  # ----------------------------------------
  # :section: Validations
  # ----------------------------------------
  validates :user, presence: true
  validates :test_profile, presence: true
  validates :test_language, presence: true
  validates_email_format_of :client_contact_email, allow_blank: true, check_mx: false
  # ----------------------------------------
  # :section: Statuses
  # ----------------------------------------
  def self.statuses
    TestInstance.aasm.states.map { |s| s.name.to_s }
  end

  after_update :set_review_required_date, if: :review_required_changed?

  include AASM
  # https://github.com/aasm/aasm
  aasm column: :status do
    # States
    state :initialized, initial: true # Object has been created
    state :ready # Test is ready to be answered by the user
    state :connected # User connected to the interface for this test
    state :audio_tested # User tested audio
    state :tutored # User viewed the tutorial
    state :in_progress # User started answering questions
    state :cancelled # User answered all questions
    state :completed # User answered all questions
    state :pending # Test instance has an issue that is being worked on
    state :validations_failed # All questions answered, but failed automatic validations
    state :validations_passed # All questions answered and passed automatic validations
    state :sent_for_evaluation
    state :graded # Evaluation completed, score received

    # Events
    event :be_ready do
      transitions from: :initialized, to: :ready
    end

    event :reset_to_ready do
      transitions to: :ready
    end

    event :first_connection do
      transitions from: %i[initialized ready], to: :connected
    end
    event :audio_test do
      transitions to: :audio_tested
    end
    event :tutorial do
      transitions to: :tutored
    end
    event :start_test, after_commit: :start_test_callback do
      transitions from: %i[initialized ready connected audio_tested tutored], to: :in_progress
    end
    event :cancel, guards: [:cancellable?], after_commit: :test_cancelled_callback do
      transitions to: :cancelled
    end
    event :complete, after_commit: :test_completed_callback do
      transitions to: :completed
    end
    event :redo_test, after_commit: :after_redo_callback do
      transitions to: :in_progress
    end
    # Validations
    event :pass_validations, after_commit: :pass_validations_callback do
      transitions from: %i[audio_tested in_progress completed validations_failed pending sent_for_evaluation],
                  to: :validations_passed
    end
    event :fail_validations, after_commit: :fail_validations_callback do
      transitions from: %i[audio_tested in_progress completed validations_passed pending sent_for_evaluation],
                  to: :validations_failed
    end

    # Grading
    event :grade, after_commit: :test_graded_callback do
      transitions to: :graded
    end

    # Pending support action
    event :pending, after_commit: :test_pending_callback do
      transitions to: :pending
    end
    event :send_for_evaluation, after_commit: [:manage_evaluations_after_sent_for_evaluation, :sent_for_evaluation_callback] do
      transitions to: :sent_for_evaluation
    end

    after_all_transitions :log_status_change
  end

  def cancellable?
    status.to_sym.in?(TestInstance::CANCELLABLE_STATUSES) || test_mode
  end

  def not_started?
    NOT_STARTED.include?(status.to_sym)
  end

  def finished?
    FINISHED_STATUSES.include?(status.to_sym)
  end

  def can_be_set_at_review_required?
    !%i[sent_for_evaluation graded].include?(status.to_sym)
  end

  def active?
    %i[initialized ready connected audio_tested tutored in_progress completed pending
       validations_failed validations_passed sent_for_evaluation].include?(status.to_sym)
  end

  def public_status
    if %i[initialized ready connected audio_tested tutored].include?(status.to_sym)
      'not started'
    elsif %i[in_progress pending].include?(status.to_sym)
      'in progress'
    elsif %i[completed validations_failed validations_passed sent_for_evaluation].include?(status.to_sym)
      'waiting_grade'
    elsif status.to_sym == :graded
      'graded'
    else
      'error'
    end
  end

  # Callbacks
  def start_test_callback
    update(begin_date: Time.now)
  end

  def test_cancelled_callback
    cancel_evaluations
    # Delete test in Pclients?
    ## Send email When test is cancelled
    MailjetHelper.send_test_canceled(self) unless @skip_cancel_notification
  end

  def status_can_change?(next_status)
    next_event = aasm.permitted_transitions.find { |pt| pt[:state] == next_status.to_sym }
    return false unless next_event

    send(:"may_#{next_event[:event]}?")
  end

  def status_disabled_for_test_mode?(next_status)
    return false unless test_mode?

    %i[validations_passed sent_for_evaluation graded validations_failed redo].include?(next_status.to_sym)
  end

  def full_reset
    reset_to_ready!
    productions.destroy_all
    receptions.destroy_all
    update(question_set_id: nil, count_of_tekis_reminders: 0)
    update_next_questionables
    redo_test!
    reset_evals
  end

  # TODO: add to EvaluationsManagerService
  def reset_evals
    cancel_evaluations

    if evaluations.created.empty?
      if evaluations.empty?
        # TODO : use the updated way to add Evaluations to TIs
        #  add_and_assign_evaluation
        evaluations << Evaluation.new_for_test_instance(self)
      else
        # TODO : use the updated way to add Evaluations to TIs
        #   add_evaluation unless test_mode
        create_evaluation
      end
      save
    end
  end

  def after_redo_callback
    update(graded_at: nil)
  end

  def create_productions_metadata
    ordered_completed_productions.each do |prod|
      next unless prod.audio_production.present?

      if prod.recording_duration <= 10
        old_recording = prod.recording
        prod.find_better_audio_production
        redo if old_recording&.id != prod&.recording&.id
      end

      prod_metadatum = prod.get_or_create_production_metadatum
      CreateProductionMetadataJob.perform_later(prod_metadatum.id)
    end
  end

  # TODO: add to EvaluationsManagerService
  def assign_to_all_examiners
    examiner_ids_to_skip = evaluations.where.not(status: [:canceled]).pluck(:examiner_id)
    Examiner.real.where(language: test_language_sym).ti_bulk_assign.where.not(id: examiner_ids_to_skip).each do |examiner|
      # TODO : rework the way to only select examiners that can_assign_evaluation?
      # TODO : use the updated way to add Evaluations to TIs
      #       add_and_force_assign_evaluation(
      #         evaluation_goal: Evaluation::EVALUATION_GOAL[:peer_review],
      #         examiner:,
      #         tags:
      #       )
      ev = Evaluation.new_for_test_instance(self)
      next unless examiner.can_assign_evaluation?(ev)[:result]

      ev.evaluation_goal = Evaluation::EVALUATION_GOAL[:peer_review]
      ev.examiner_id = examiner.id
      ev.save!
      ev.force_assign!('assigned_at')
    end
  end

  def api_order_information(information: nil)
    objectized_api_oi = JSON.parse(api_order.order_information['params'].to_json, object_class: OpenStruct)
    result = browse_open_struct_for_information(information: information, open_struct: objectized_api_oi)
    result.is_a?(OpenStruct) ? nil : result
  end

  def browse_open_struct_for_information(information: nil, open_struct: nil)
    open_struct.each_pair do |key, value|
      if value.is_a?(OpenStruct)
        result = browse_open_struct_for_information(open_struct: value, information: information)
      elsif key.to_s == information
        return value
      end
      return result if result.present? && !result.is_a?(OpenStruct)
    end
  end

  def identity_photos_urls
    sample_identity_photos(Rails.application.config.s3[:identity_bucket].objects(prefix: "#{Rails.env}/ti#{id}")).map { |photo| photo.presigned_url(:get) }
  end

  private

  def sample_identity_photos(objects)
    samples_count = 10

    return objects if objects.count <= samples_count

    step = (objects.count.to_f / samples_count).floor
    objects.each_slice(step).map(&:first).take(samples_count)
  end

  def test_completed_callback
    update(end_date: Time.now)

    ## Send email confirmation. Do not do it if TI is set at skip_email
    MailjetHelper.send_test_completed(self) unless skip_email?
    # Trigger test validations if !test_mode
    if test_mode
      MailjetHelper.send_demo_email(self)
    else
      if Rails.env.production?
        ValidateTestInstanceJob.set(wait: 8.minutes).perform_later(id)
      else
        ValidateTestInstanceJob.set(wait: 5.minutes).perform_later(id)
      end
      IdentityJobs::IdentityValidationJob.perform_later(self) unless skip_identity_check
      create_productions_metadata
    end
    close_tekis
  end

  # Callback after grade becomes available and needs to be shared:
  # - email
  # - pipplet-clients
  # - smart-recruiter
  def test_graded_callback
    MailjetHelper.send_certificate_user(self) if certificate_sent_to_candidate

    update(graded_at: Time.now)
    close_tekis
    api_order&.test_graded_callback
  end

  def pass_validations_callback
    ## Normally here only !test_mode test_instances should arrive

    send_for_evaluation! unless review_required

    delete_all_redo
  end

  def sent_for_evaluation_callback
    update(sent_for_evaluation_date: Time.now)
    close_tekis
  end

  def manage_evaluations_after_sent_for_evaluation
    EvaluationsManagerService.new(test_instance: self).on_test_instance_sent_for_evaluation
  end

  public

  def close_tekis
    technical_issues.map(&:close!)
  end

  def fail_validations_callback
    ## Normally here only !test_mode test_instances should arrive
    create_teki
  end

  def create_teki(status: 'Incomplete', from: 'validation', teki_category: nil, force: false)
    CreateTechnicalIssueJob.perform_later(test_instance_id: self.id, status: status, from: from, teki_category: teki_category) if force || !has_in_progress_tekis?
  end

  def set_review_required_date
    update_column(:review_required_modified_at, Time.now)
  end

  def has_in_progress_tekis?
    technical_issues.in_progress.present?
  end

  def get_delivered_eval
    delivered_evaluation || evaluations.delivered.order(delivered_at: :desc).first
  end

  def overall_grade
    get_delivered_eval&.overall_grade
  end

  def mark_as_graded(evaluation)
    self.result_rating = evaluation.overall_score
    self.delivered_evaluation_id = evaluation.id
    self.report = evaluation.pdf_certificate
    save!
    grade!('graded_at') if may_grade? && (graded_at.nil? || regrading?)
  end

  def regrading?
    regrade_requested_at.present?
  end

  def check_duplicate
    sanitized_first_name = SanitizerHelper::SQL.sanitize(user.first_name)
    sanitized_last_name = SanitizerHelper::SQL.sanitize(user.last_name)

    return [] unless sanitized_first_name.present? && sanitized_last_name.present?

    ShardsHelper.reading_replica_on_production do
      TestInstance.joins(:user)
                  .where("#{SanitizerHelper::SQL.sanitize_sql_var('users.first_name')} = ?", sanitized_first_name)
                  .where("#{SanitizerHelper::SQL.sanitize_sql_var('users.last_name')} = ?", sanitized_last_name)
                  .where.not(id:)
                  .where(test_language:)
                  .graded
                  .order(graded_at: :asc)
    end
  end

  def manage_duplicates(current_eval)
    duplicates = check_duplicate.joins(delivered_evaluation: { certificate: :grades }).where(certificate: { grades: { label: 'overall' } }).where.not(certificate: { grades: { score: 0 } }).distinct

    # Check if user already has graded test instances with the same language
    ## TODO check also user with same name but different user_id
    if !duplicates.empty? && !current_eval.trial?

      duplicates_id = duplicates.collect(&:id)

      self.duplicate = "Duplicate of : #{duplicates_id}"
      EventLog.add(self, :duplicate, "Duplicate has been identified : #{duplicates_id}", :warning)
      save!

      ## Get the preceding TI and preceding eval
      old_ti = duplicates.last
      EventLog.add(self, :duplicate, "Duplicate Selected is #{old_ti.id}", :warning)
      old_eval = old_ti.evaluations.delivered.where.not("json_assessment::jsonb = '{}'::jsonb").where.not(json_assessment: nil).first

      ## Check if all necessary data is here, otherwise send an alert
      if current_eval.nil? || old_eval.nil? || current_eval.certificate.nil? || old_eval.certificate.nil?
        message = "Current Test Instance being assessed was : #{id}. Evaluation passed as argument was : #{current_eval&.id}"
        Alert.system('Candidate with 2 Test Instances Graded detected but not managed', message)
        EventLog.add(self, :duplicate,
                     "Candidate with 2 Test Instances Graded detected but not managed :  #{message}", :danger)
        return
      end

      ## Duplicate management.
      ## Rule is : Don't send a score that's not at least X sublevel over the previous score.
      ## X being 0 for any client and can be any value for specific client
      ## If new score is at least X sublevel over the previous score, let's go
      ## If not, create a duplicate of X sublevel over the previous score
      ## Get the current and old score and calculate the difference in percentage
      old_score = CECRL_LEVELS_VALUES[:cecrl].index(old_eval.certificate.grades.overall.last.cecrl_score)
      new_score = CECRL_LEVELS_VALUES[:cecrl].index(current_eval.certificate.grades.overall.last.cecrl_score)
      score_diff = new_score - old_score

      EventLog.add(self, :duplicate,
                   "[Duplicate Management] Old score is #{old_score}, New score is #{new_score}, Diff is #{score_diff}", :info)

      minimum_score_difference = CLIENTS_INCREASE_LEVELS[client_name].to_i || 0

      ## If we have a score difference within the acceptable area (0 to 3), then nothing to do
      if score_diff >= minimum_score_difference && score_diff <= 3
        EventLog.add(self, :duplicate, '[Duplicate Management] Score in acceptable range', :info)
        return
      elsif score_diff < minimum_score_difference
        EventLog.add(self, :duplicate, '[Duplicate Management] New Score is too low, create admin eval', :info)
        increase_by = old_score - new_score + minimum_score_difference
        EventLog.add(self, :duplicate, "[Duplicate Management] Increase by #{increase_by}", :info)
        new_eval = current_eval.duplicate_with_adjusted_score(increase_by, old_eval.certificate.grades.collect(&:label), Evaluation::EVALUATION_GOAL[:standard])
        new_eval.assess_no_callback!('assessed_at')
        new_eval.save!
      else
        return
      end

      duplicates.each do |ti|
        e = ti.evaluations.delivered.last
        ti.duplicate = "TI graded#{e.nil? ? ' -not delivered' : ''}"
        ti.save!
      end
    end
  end

  def calc_trusted_average_grades
    completed_evals = evaluations.with_certificate.completed.with_real_examiner

    review_evals = completed_evals.where(is_reviewed: true)
    # exclude normal evals if their exists an eval review
    evals_with_no_reviews = completed_evals.where(is_reviewed: false).where.not(examiner_id: review_evals.pluck(:examiner_id))

    processed_evals = review_evals + evals_with_no_reviews
    trusted_completed_evals = processed_evals.select { |e| e.examiner.trusted? }

    # Calc average if more than 1 trusted exam eval and 1 other eval (not reviewed)
    if (trusted_completed_evals.size >= 1) && (processed_evals.size >= 2)
      trusted_eval_ids = trusted_completed_evals.map(&:id)

      ## Get all available labels
      assessment_types = trusted_completed_evals.pluck(:assessment_type_id)
      labels = []
      assessment_types.each do |a_t|
        labels |= AssessmentType.find(a_t).json_calculation_rules.keys
      end

      labels.each do |label|
        new_average_grade = AverageGrade.create_with_eval_ids(eval_ids: trusted_eval_ids, label: label, ti: self)
        average_grades << new_average_grade unless new_average_grade.nil?
      end
    end
  end

  def calc_basic_average_grades
    completed_evals = evaluations.with_certificate.completed.with_real_examiner
    eval_ids = completed_evals.pluck(:id)

    unless eval_ids.empty?

      ## Get all available labels
      assessment_types = completed_evals.pluck(:assessment_type_id)
      labels = []
      assessment_types.each do |a_t|
        labels |= AssessmentType.find(a_t).json_calculation_rules.keys
      end

      labels.each do |label|
        new_average_grade = AverageGrade.create_with_eval_ids(eval_ids: eval_ids, label: label, ti: self)
        average_grades << new_average_grade unless new_average_grade.nil?
      end
    end
  end

  def calc_average_grades
    average_grades.destroy_all
    trusted_completed_exam_evals = evaluations.joins(:examiner).with_certificate.completed.with_real_examiner.where(examiners: { status: Examiner.statuses[:trusted] })

    if trusted_completed_exam_evals.empty?
      calc_basic_average_grades
    else
      calc_trusted_average_grades
    end
    save!
  end

  # TODO: add to EvaluationsManagerService
  def test_pending_callback
    evaluations = self.evaluations.assigned + self.evaluations.created

    unless evaluations.empty?
      evaluations.each do |eval|
        eval.status = 'canceled'
        eval.save!
      end
    end

    # TODO : use the updated way to add Evaluations to TIs
    #  add_evaluation unless test_mode
    create_evaluation
  end

  def is_training_profile?
    test_profile.name.in?(TEST_PROFILE_FOR_FORMATION)
  end

  # ----------------------------------------
  # :section: Callbacks
  # ----------------------------------------
  after_create do |t|
    # NextQuestionable initialisation
    t.initialize_test

    # Assign default examiner
    t.assign_default_examiner!

    # Is client forbidden from receiving reminders
    t.update_column(:send_reminders, false) if NO_REMINDERS_CLIENTS.include?(user.group)

    # Save the initial number of questions to participate
    t.refresh_cached_count_of_questions_to_answer
    t.refresh_cached_count_of_questions_answered

    # Create associated evaluation
    # TODO : use the updated way to add Evaluations to TIs
    #   add_evaluation unless test_mode
    t.create_evaluation
    # Check if there are specific client configs actions to perform
    client_config&.apply_specific_rules_to_ti(t.id)
    t.update_for_talogy
  end

  before_save do |t|
    # Move status to ready automatically on save
    if t.may_be_ready? &&
       t.test_profile.is_usable? &&
       !t.next_questionable_profile.nil? &&
       !t.test_language.nil?

      t.be_ready
    end

    # Ensure we have a UUID
    if t.uuid.nil?
      loop do
        t.uuid = SecureRandom.uuid
        break unless TestInstance.exists?(uuid: t.uuid)
      end
    end
  end

  def strong_identity_check?
    @strong_identity_check ||= client_config&.strong_identity_check.present?
  end

  def update_for_talogy
    # 20220429 TEMPORARY FIX FOR CUBIKS - TO UPDATE WHEN CLIENT WILL UPDATE ITS REQUEST
    if client_name&.include?('Cubiks')
      Rails.logger.info('[Talogy/Cubiks][API] still use the wrong client_name')
      update_column(:client_name, 'Talogy')
    else
      Rails.logger.info('[Talogy/Cubiks][API] use the good client_name. You can delete this method.')
    end
  end

  # TODO : remove when all occurrences are replaced by add_evaluation
  def create_evaluation
    return if test_mode

    new_evaluation = Evaluation.new_for_test_instance(self)
    if new_evaluation
      evaluations << [new_evaluation]
      save
    else
      Alert.system('[Evaluation]', "Evaluation creation has failed TI id = #{id}")
    end
  end

  # TODO: add to EvaluationsManagerService
  def assign_graded_evaluation_with_score(score, assessment_type)
    json = {}

    a = AssessmentType.find(assessment_type)
    keys = a.get_all_scorable_keys

    keys.each do |key|
      id = a.find_assessment_question_by_key(key)['assessment_question_id']
      notes = AssessmentQuestion.find(id).json_data['choices'].to_a

      index = notes.size - (notes.size * score / 100) - 1
      index = 0 if index.negative?

      json[key] = notes[index]
    end

    # TODO : use the updated way to add Evaluations to TIs
    #       test_instance.add_and_force_assign_evaluation
    #         examiner: Examiner.pipplet_admin_examiner(test_language),
    #         certification_type: a.name,
    #         assessment_type_id: a.id,
    #         evaluation_goal:,
    #         json_assessment: json,
    #         tags:
    #       )
    #     e.generate_certificate
    #     e.assess!('assessed_at')
    #     GenerateEvaluationCertificateJob.perform_later(e.id, false, false)

    e = Evaluation.new_for_test_instance(self)
    evaluations << [e]

    e.examiner = Examiner.pipplet_admin_examiner(test_language)
    EventLog.add(self, :admin, 'assign_graded_evaluation_with_score# Assign admin examiner', :info)
    e.status = 'assigned'
    e.assigned_at = Time.now

    e.certification_type = a.name
    e.assessment_type_id = a.id
    e.json_assessment = json
    e.generate_certificate
    e.status = 'assessed'
    e.assessed_at = Time.now
    e.save!

    GenerateEvaluationCertificateJob.perform_later(e.id, false, false)
    e
  end

  def cancel_evaluations
    evaluations.can_be_canceled.each { |ev| ev.cancel!('canceled_at') }
  end

  def is_section_full_skipped?(section)
    return false unless Production.respond_to?(section)

    productions.exclude_ignored.send(section).pluck(:status).uniq == ['skipped']
  end

  def is_full_skipped_or_being_answered?
    productions.exclude_ignored
               .pluck(:status)
               .difference(%w[skipped being_answered])
               .blank?
  end

  # ----------------------------------------
  # :section: Instance methods
  # ----------------------------------------
  delegate :next_questionable_profile, to: :test_profile, allow_nil: true
  delegate :source, to: :api_order, allow_nil: true

  def perform_identity_check?
    !skip_identity_check
  end

  def redo_questionable(questionable_type, questionable_id)
    # Extract questionable
    questionable = case questionable_type
                   when 'production'
                     Production.order(:id).find_by(id: questionable_id)
                   when 'reception'
                     Reception.order(:id).find_by(id: questionable_id)
                   end
    if questionable
      # Flag old :questionable_id
      questionable.redo!
      # Add new one to the queue
      update_next_questionables
      # Update test_instance status
      redo_test! # if !self.has_answered_enough_questions?

      # TODO: add to EvaluationsManagerService as a new method add_evaluations_to_be_redone
      evaluations.completed.each do |e|
        next if e.examiner.is_ai? && qc_ai?
        next unless evaluations.created.where(examiner_id: e.examiner_id,
                                              evaluation_goal: e.evaluation_goal).empty?

        # TODO : use the updated way to add Evaluations to TIs
        #       test_instance.add_evaluation(
        #        <useful_params_from_current_evaluation>
        #       )
        #
        new_eval = e.dup
        new_eval.status = 'created'
        new_eval.created_at = Time.now
        new_eval.assigned_at = nil
        new_eval.reminder_nb = 0
        new_eval.completed_at = nil
        new_eval.invoice_request_id = nil
        new_eval.assessed_at = nil
        new_eval.delivered_at = nil
        new_eval.presented_for_review_at = nil
        new_eval.presented_for_review = false
        new_eval.reset_waiting_assessment!
        new_eval.is_reviewed = true
        new_eval.save!
        EventLog.add(new_eval, :admin, "Dup##{e.id}#Created for redo", :info)
      end

      evaluations.can_be_reset.each(&:reset!)

      update(end_date: nil)
      cancel_evaluations
      evaluations << Evaluation.new_for_test_instance(self)
      save
      true
    else
      false
    end
  end

  def redo_all(answer_type)
    productions = self.productions
    productions&.each do |p|
      redo_questionable(p.type.to_s, p.id) if p.answer_type == answer_type
    end
  end

  def admin_url
    user.account_registration_url
  end

  def test_url
    if user.identity_provider.present?
      user.identity_provider.auth_request_url(user.email)
    else
      user.account_registration_url
    end
  end

  def secure_browser_url
    user.account_registration_url({ protocol: 'wheebox' })
  end

  def send_email_notification_to_candidate!
    return false if skip_email?

    MailjetHelper.send_reminder(self, false)
  end

  def send_last_chance_to_grade_tests_alert!; end

  # ----------------------------------------
  # :section: Sources
  # ----------------------------------------
  # Guess the recruiter's name, to be displayed in communications to the user
  def client_contact_full_name
    [client_contact_first_name || '', client_contact_last_name || ''].join(' ').strip
  end

  def get_certificate_email_destination
    client_config&.surcharged_certificate_email.blank? ? client_contact_email : client_config.surcharged_certificate_email
  end

  def save_validation_details(validation_details)
    if test_instance_validation_detail.present?
      test_instance_validation_detail.update(validation_details: validation_details.as_json)
    else
      TestInstanceValidationDetail.create!(
        test_instance_id: id,
        validation_details: validation_details.as_json
      )
    end
  end

  def get_already_used_examiner_ids
    evaluations.active.pluck(:examiner_id)
  end

  # ----------------------------------------
  # :section: Test validation & Evaluation
  # ----------------------------------------
  # Super basic question validations
  def questions_passed_validations?
    passed = true
    total_audio_duration = 0
    total_text_length = 0
    skipped = 0
    timeout = 0
    count_of_answered_questions = 0
    checks = []

    ordered_completed_productions.each_with_index do |production, index|
      audio_duration = production.recording_duration
      text_length = production.text_length

      checks[index + 1] = true

      ## Don't assess the first question at all
      next if index.zero?

      # Save total length but skip 1 question
      total_audio_duration += audio_duration
      total_text_length += text_length

      count_of_answered_questions += 1 if audio_duration.positive? || text_length.positive?

      if production.skipped?
        skipped += 1
        EventLog.add(production, :validation, 'Skipped', :warning)
        next
      elsif production.timeout? && audio_duration.zero? && text_length.zero?
        timeout += 1
        EventLog.add(production, :validation, 'Timeout :', :warning)
        next
      end

      minimum_length = 20

      minimum_length = [(production.text_production_q_element.minimum_length(test_language) * 0.3).to_i, 150].min if production.has_text_production? && !production.text_production_q_element.nil?

      # Check that either audio is greater than 5 sec or text is longer than 20 characters
      unless production.admin_answered?
        if audio_duration.zero? && text_length.zero?
          EventLog.add(production, :validation, 'Not Answered :', :warning)
          production.unanswered!
          checks[index + 1] = false
        elsif !(audio_duration > 10 || text_length > minimum_length)
          EventLog.add(production, :validation, 'Too Short :', :warning)
          production.too_short!
          checks[index + 1] = false
        elsif production.production_metadatum.present? && production.production_metadatum.updated_transcription?
          validations = production.production_metadatum.validations

          unless validations[:minium_transcription_length]
            EventLog.add(production, :validation, 'Transcription is to short', :warning)
            production.too_short!
            checks[index + 1] = false
          end
          # Check if audio transcription text equal the test language
          unless validations[:identified_language]
            EventLog.add(production, :validation, 'Language Failed :', :warning)
            production.language_failed!
            checks[index + 1] = false
          end
        end

        if production.text_length.positive? && perform_check_characters? && !production.check_caracters_match_with_language
          EventLog.add(production, :validation, 'Language Failed :', :warning)
          production.language_failed!
          checks[index + 1] = false
        end
      end

      ## If we are still checks true here, it means we have passed validations.
      ## If we are at a being answered or waiting moderation state, that means that we are all good and we go to validations_passed status
      ## If we are at another status (ex : timeout), we keep that status for tracing back
      if (checks[index + 1] == true) && (production.being_answered? || production.waiting_moderation?)
        EventLog.add(production, :validation, 'Validation Passed', :warning)
        production.validations_passed!
      end

      EventLog.add(self, :validation, "questions_passed_validations? : #{checks[index + 1]}", :info)
      EventLog.add(self, :validation,
                   "Production##{production.id}. Status : #{production.status} recording_duration: #{audio_duration} sec | text_length: #{text_length} characters", :info)
    end

    ## If all questions except the first one are empty
    if ((skipped + timeout) == (ordered_completed_productions.size - 1)) && !valid_audio_and_text_size?(total_audio_duration, total_text_length)
      EventLog.add(self, :validation, 'Full skip/timeout : deliver_no_answers_evaluation', :warning)
      evaluation = evaluations.created.last
      evaluation.deliver_no_answers_evaluation if evaluation.present?
      passed = true
    elsif checks[4] == false || (checks[3] == false && checks[2] == false) || (checks[5] == false && checks[6] == false) || checks[7] == false
      passed = false
    end
    EventLog.add(self, :validation, "Checks :#{checks}", :warning)
    EventLog.add(self, :validation, "Passed :#{passed}", :warning)
    EventLog.add(self, :validation, "Audition :#{total_audio_duration}", :warning)
    EventLog.add(self, :validation, "Text length :#{total_text_length}", :warning)

    # Launch new validation if validation object exists
    # if self.test_instance_validation.present?
    #   TestInstanceValidationDetailsJob.perform_now(self.id, passed)
    # end

    passed
  end

  def valid_audio_and_text_size?(total_audio_duration, total_text_length)
    valid_audio_and_text_size_event_log(total_audio_duration, total_text_length)
    audio = productions.audio.any?
    written = productions.written.any?

    return false if !audio && !written

    if audio && !written
      total_audio_duration > DEFAULT_TOTAL_AUDIO_DURATION
    elsif written && !audio
      total_text_length > minimum_total_written_duration
    else
      total_audio_duration > DEFAULT_TOTAL_AUDIO_DURATION || total_text_length > minimum_total_written_duration
    end
  end

  def valid_audio_and_text_size_event_log(total_audio_duration, total_text_length)
    audio = productions.audio.any?
    written = productions.written.any?
    if !audio && !written
      EventLog.add(self, :admin, 'The total audio length is equal to zero.', :info)
      EventLog.add(self, :admin, 'The total number of characters is equal to zero.', :info)
      return
    end

    if audio
      if total_audio_duration <= DEFAULT_TOTAL_AUDIO_DURATION
        EventLog.add(self, :admin, 'The total audio length is less than or equal to the minimum required.', :info)
      elsif total_audio_duration > DEFAULT_TOTAL_AUDIO_DURATION
        EventLog.add(self, :admin, 'The total audio length is greater than the minimum required.', :info)
      end
    end

    if written
      if total_text_length <= minimum_total_written_duration
        EventLog.add(self, :admin, 'The total number of characters is less than or equal to the minimum required.', :info)
      elsif total_text_length > minimum_total_written_duration
        EventLog.add(self, :admin, 'The total number of characters is greater than the minimum required.', :info)
      end
    end
  end

  def minimum_total_written_duration
    @minimum_total_written_duration ||= I18n.t('minimum_skip_length', locale: test_language).to_i
  end

  def perform_check_characters?
    return false if (disable_for_test_language = DISABLE_CHECK_CHARACTERS[test_language_sym]) && test_profile_id.in?(disable_for_test_language)

    client_config.nil? || !client_config.skip_check_caracters
  end

  def assign_default_examiner!
    # Try to find a group examiner for that language
    examiner = user.user_group.examiner_for(test_language_sym) if user.user_group

    update(examiner: examiner || Examiner.first_for(test_language_sym))
  end

  def launch_specific_validations
    if test_instance_validation.present?
      test_instance_validation.get_validation_result(self)
    else
      # This is the default validation process that mustn't be used anymore
      questions_passed_validations?
    end
  end

  def validate_questions!
    if %i[completed in_progress].include?(status.to_sym)
      if launch_specific_validations && !has_in_progress_tekis?
        pass_validations!
      else
        fail_validations!
      end
    end
  end

  def collect_evaluation_tags!(evaluation)
    tag_names = tags.pluck(:name).union(Tag::EVALUATION[:collected_on_test_instance] & evaluation.tags.pluck(:name))
    update!(tags: Tag.where(name: tag_names))
  end

  # set test mode
  def set_test_mode(b)
    update({ test_mode: b })
    if test_mode
      evaluations.where.not(status: %i[assessed delivered]).each(&:destroy)
    else
      # TODO : use the updated way to add Evaluations to TIs
      #   add_evaluation unless test_mode
      create_evaluation
    end
  end

  # set review required
  def set_review_required(b)
    update({ review_required: b })
    unless review_required
      send_for_evaluation! if validations_passed?
      save!
    end
  end

  # ----------------------------------------
  # :section: Email reminders
  # ----------------------------------------
  def disable_email_reminders!
    update(send_reminders: false)
  end

  def unsusbscribe_url
    Rails.application.routes.url_helpers.disable_email_reminders_test_instance_url(uuid)
  end

  # ----------------------------------------
  # :section: REMINDERS && Those function are called from the Job SendEmailRemindersJob
  # ----------------------------------------

  # Class methods
  def self.send_reminders!(really_send_email = true)
    tis_with_tekis = ShardsHelper.reading_replica_on_production do
      TestInstance.not_completed.available_for_tekis_reminder
    end

    ti_without_tekis = ShardsHelper.reading_replica_on_production do
      TestInstance.not_completed
                  .available_for_reminder
                  .where.not(id: tis_with_tekis.pluck(:id))
    end
    ti_without_tekis.find_each { |ti_without_tekis| ti_without_tekis.send_reminder!(really_send_email) }
    tis_with_tekis.find_each(&:send_tekis_reminders!)
  end

  def self.send_sms_reminders!
    TestInstance.real_with_sms_reminders.each do |test_instance|
      SendSmsUsersJob.perform_later(
        test_instance.id,
        I18n.t('reminders.sms', client_name: test_instance.client_contact_full_name, client_company: test_instance.client_name, ti_language: I18n.t("languages.#{test_instance.test_language}"))
      )
      test_instance.update(
        last_sms_reminder_sent_at: DateTime.current,
        count_of_sms_reminders: test_instance.count_of_sms_reminders + 1
      )
    end
  end

  # Instance methods called by class methods
  def send_tekis_reminders!
    if count_of_tekis_reminders < TechnicalIssue::MAX_TEKI_REMINDERS
      MailjetHelper.send_candidate_tekis_reminders(self)
    else
      MailjetHelper.send_clients_tekis_reminders(self)
    end
    update(count_of_tekis_reminders: count_of_tekis_reminders + 1, last_tekis_email_send_at: DateTime.now)
  end

  def send_reminder!(really_send_email = true)
    force_send_reminder! if really_send_email
  end

  def force_send_reminder!
    return false if skip_email?

    MailjetHelper.send_reminder(self, true)
    update(
      count_of_reminders_sent: (count_of_reminders_sent + 1),
      last_reminder_sent_at: Time.now
    )
  end

  # ----------------------------------------
  # :section: Accessors & stats about answered questions
  # ----------------------------------------
  def ordered_completed_productions
    return [] if ordered_challenge_ids.blank?

    ordered_completed_productions_array = []

    ordered_challenge_ids.each do |c_id|
      p = productions.exclude_ignored.find_by(challenge_id: c_id)
      ordered_completed_productions_array << p if p
    end

    ordered_completed_productions_array
  end

  def ordered_challenge_ids
    # If question_set_id is null, it means that we are running the default test
    if !question_set_id || question_set_id == -1

      fallback_set_list = test_profile.fallback_set
      if fallback_set_list.blank?
        Challenge.order_list_by_linguist_ids(LINGUIST_IDS_FOR_DEFAULT_AUDITION_TEST).map(&:id)
      else
        Challenge.order_list_by_linguist_ids(fallback_set_list).map(&:id)
      end
    else
      full_challenge_id_array = get_full_challenge_id_array
      if full_challenge_id_array.size == 1
        full_challenge_id_array.first
      else
        full_challenge_id_array += test_profile.additional_challenge_id_array if test_profile.has_additional_translated_language?(test_language) && !test_profile.additional_linguist_ids.blank?
        full_challenge_id_array[question_set_id]
      end
    end
  end

  def ordered_challenge_linguist_ids
    # If question_set_id is null, it means that we are running the default test
    if !question_set_id || question_set_id == -1

      fallback_set_list = test_profile.fallback_set
      if fallback_set_list.blank?
        LINGUIST_IDS_FOR_DEFAULT_AUDITION_TEST
      else
        fallback_set_list.split(';')
      end
    else
      test_profile.challenge_linguist_ids_array[question_set_id]
    end
  end

  # Total number of questions to be answered by a user
  # @return:integer
  def count_of_questions_to_answer
    if cached_count_of_questions_to_answer.nil?
      refresh_cached_count_of_questions_to_answer
    else
      cached_count_of_questions_to_answer
    end
  end

  def refresh_cached_count_of_questions_to_answer
    value = next_questionable_profile_module.count_of_questions_to_answer(self)
    update(cached_count_of_questions_to_answer: value)
    value
  end

  # Total number of questions already answered by a user
  # @return:integer
  def count_of_questions_answered
    if cached_count_of_questions_answered.nil?
      refresh_cached_count_of_questions_answered
    else
      cached_count_of_questions_answered
    end
  end

  def refresh_cached_count_of_questions_answered
    return unless next_questionable_profile_module.respond_to? :count_of_questions_answered

    value = next_questionable_profile_module.count_of_questions_answered(self)
    update(cached_count_of_questions_answered: value)
    value
  end

  # Percentage of questions participated over the minimum required threshold
  # @return: float between 0 and 100 (used as percentage)
  def ratio_of_required_questions_answered
    return 0 if count_of_questions_to_answer.nil? || count_of_questions_to_answer <= 0

    r = count_of_questions_answered / count_of_questions_to_answer.to_f * 100
    r > 100 ? 100 : r
  end

  def createCertificate(certif_param)
    certificate&.destroy!
    self.certificate = Certificate.createCertificate(certif_param)
    save
  end

  def check_grades_for_client?
    client_config.nil? ? false : (client_config.systematic_peer_review.positive? and client_config.systematic_peer_review_counter.positive?)
  end

  def can_compute_client_check_eval?
    return false unless evaluations.client_grade_checks.any?

    ref_eval, peer_review_evals = get_eval_for_client_check_computing
    wanted_nb_peer_eval = client_config.systematic_peer_review
    ref_eval.present? and peer_review_evals.count == wanted_nb_peer_eval
  end

  def get_eval_for_client_check_computing
    ref_eval = evaluations.completed.client_grade_checks.take
    wanted_nb_peer_eval = client_config.systematic_peer_review
    peer_review_evals = evaluations.with_real_examiner.completed.peer_reviews.last(wanted_nb_peer_eval)
    [ref_eval, peer_review_evals]
  end

  def client_check_eval_errors
    return [] unless can_compute_client_check_eval?

    ref_eval, global_diff = get_ref_eval_and_diff_for_client_check_eval

    return ['Client Grade Check: Large difference'] if global_diff.abs > 2

    []
  end

  def get_ref_eval_and_diff_for_client_check_eval
    ref_eval, peer_review_evals = get_eval_for_client_check_computing
    ref_overall_grade = get_cecrl_score(ref_eval.score)
    ## Get the average of all these grades. This will be the score of the newly created eval
    global_mean_overall_grade = get_cecrl_score((peer_review_evals.sum(&:score) + ref_eval.score) / (peer_review_evals.count + 1))

    global_diff = CECRL_LEVELS_VALUES[:cecrl].index(global_mean_overall_grade) - CECRL_LEVELS_VALUES[:cecrl].index(ref_overall_grade)
    [ref_eval, global_diff]
  end

  # TODO: add to EvaluationsManagerService
  def compute_client_check_eval
    return false unless can_compute_client_check_eval?

    check_details_array = []

    EventLog.add(self, :process, 'Compute Client Check Eval', :info)
    ref_eval, global_diff = get_ref_eval_and_diff_for_client_check_eval

    new_eval = ref_eval.duplicate_with_adjusted_score(global_diff, ref_eval.certificate.grades.pluck(:label),
                                                      Evaluation::EVALUATION_GOAL[:standard])

    ref_eval, peer_review_evals = get_eval_for_client_check_computing
    new_eval.clone_blocking_explanations!(evaluations: (peer_review_evals << ref_eval))
    new_eval.add_tags_from_assessment_rules

    check_details_array << 'Client_grade_check'
    checks_failed = ref_eval.check_evaluation_for_certificate_issue[:status] == false

    if checks_failed
      EventLog.add(self, :process, 'Ref. Eval checks do not pass, stop', :info)
      check_details_array << 'Client_grade_check evaluation checks do not pass'
    end

    new_eval.check_details = check_details_array.to_s
    new_eval.save!

    return if checks_failed

    ## If diff between first eval and the average is <= 2 then we deliver. Otherwise it goes to stuck evaluations
    if global_diff.abs <= 2
      EventLog.add(self, :process, 'Client Check Eval diff <= 2, deliver', :info)
      GenerateEvaluationCertificateJob.perform_later(new_eval.id, false, true, true)
    else
      EventLog.add(self, :process, 'Client Check Eval diff > 2, stop', :info)
    end
  end

  def need_strong_id_check?
    client_config.nil? ? false : (perform_identity_check? and client_config.strong_identity_check)
  end

  def practice_pack_mention?
    client_config&.practice_pack_mention.nil? || client_config.practice_pack_mention
  end

  def has_fake_email?
    return false if direct_user.nil?

    direct_user.fake_emails?
  end

  # TODO: clean this method
  def mark_evaluation_as_delivered(evaluation_id)
    e = Evaluation.find_by_id(evaluation_id)
    if e.present?
      e.deliver!('delivered_at')
      EventLog.add(e, :process, "Evaluation #{e.id} delivered by TI graded callback", :info)
    else
      Alert.api('[Api::Grade::MarkEvaluationAsDelivered] Evaluation not found', evaluation_id.to_s)
    end
  end

  ## Go through all text productions to get average typing speed
  def get_average_typing_speed
    speeds = []
    ordered_completed_productions.each do |prod|
      speeds << prod.production_metadatum.typing_speed if prod.answer_type == :written && !prod.production_metadatum&.typing_speed.nil?
    end
    speeds.empty? ? nil : speeds.sum.to_int / speeds.size.to_int
  end

  def last_production_before?(from:)
    return false if ordered_completed_productions.blank?

    ordered_completed_productions.max_by { |production| production.displayed_at.to_i }
                                 .displayed_at
                                 .before?(from)
  end

  # ----------------------------------------
  # :section: Create USER & TEST_INSTANCE
  # ----------------------------------------

  def self.find_trial_tis(lang, nb_selected_trusted_exam, cecrl_score, certif_type, rm_exam_id)
    tis = TestInstance.graded.joins(
      :test_profile,
      :average_grades,
      evaluations: :examiner
    ).includes(
      :productions,
      :evaluations
    ).where(
      'graded_at > ? AND test_instances.created_at::date >= ?', 24.months.ago, '2021-12-01'.to_date
    ).where(
      test_profiles: { certification_type: certif_type },
      test_language: lang,
      average_grades: { label: 'overall', cecrl_score: cecrl_score },
      examiners: { real: true }
    ).group(:id).having("COUNT(*) FILTER (WHERE examiners.status = ? AND evaluations.status IN ('assessed', 'delivered')) >= ?", Examiner.statuses[:trusted], nb_selected_trusted_exam).having("COUNT(*) FILTER (WHERE evaluations.examiner_id = ?) = 0", rm_exam_id).order(graded_at: :desc).limit(50)

    tis.sort_by do |ti|
      ti.ordered_completed_productions.sum(&:recording_duration) + ti.ordered_completed_productions.sum(&:text_length)
    end.reverse.select do |ti|
      evaluation = ti.evaluations.delivered.order(delivered_at: :desc).first
      (evaluation.get_json_assessment_value('assessment_robustness').to_i > 6 && evaluation.get_json_assessment_value('assessment_confidence').to_i > 6) ||
        !Tag::EVALUATION[:security_violation].intersect?(evaluation.tags.map(&:name))
    end
  end

  # @input: the parameter must be a TestInstanceRequest Object (check the code in lib/test_instance_request.rb)
  # @return: this method returns a TestInstance object, linked to a User object.

  def self.create_with_user(request)
    # Clean-up params. Extract group as well.
    request.sanitize_params!

    # This method WILL raise exceptions if params are not valid.
    # This is meant to send an email to Pipplet Admin to address this issue ASAP.
    request.validate_params!

    # ---  Get or create user --- #
    user = User.find_by(email: request.user.email)
    if user
      # Update user with latest info
      user.skip_cgu_and_password_validations!
      user.update({
                    first_name: request.user.first_name,
                    last_name: request.user.last_name,
                    phone_number: request.user.phone_number,
                    group: request.user.group,
                    language_name: request.user.locale
                  })

    else
      user = User.new(
        email: request.user.email,
        first_name: request.user.first_name,
        last_name: request.user.last_name,
        phone_number: request.user.phone_number,
        group: request.user.group,
        language_name: request.user.locale
      )
      user.skip_cgu_and_password_validations!
      user.skip_confirmation_notification!
      user.save
    end

    # Ensure that we have a valid user
    if user.valid?
      user.created!
    else
      return [user, nil]
    end

    # Cancel previous test mode instances for this user
    TestInstance.not_cancelled.where({ user_id: user.id, test_mode: true }).each(&:cancel!)

    # ---  Manage existing test_instance --- #
    ti = TestInstance.not_test_mode.not_cancelled.find_by({
                                                            user_id: user.id,
                                                            test_profile_id: request.test_instance.test_profile_id,
                                                            test_language: request.test_instance.test_language,
                                                            created_at: 10.seconds.ago..
                                                          })

    # We simply send some notification to the pipplet team, but we continue
    if ti && !ti.graded? && !is_request_api?(request)
      m = "[TestInstance.create_with_user] User #{user.id} is taking the same test again, TestLanguage: #{request.test_instance.test_language} TestProfile #{ti.test_profile.name} (#{ti.test_profile_id}))\n\n"
      m += "Source: '#{request.source.source_id}'\n"
      m += "Source type: '#{request.source.source_type}'\n"
      m += "User: #{user.full_name_with_id}"
      s = "[REVIEW REQUIRED][#{request.source.source_type} ##{request.source.source_id}]  User #{user.full_name_with_id} is being asked take the same test again"

      Alert.test_instance_activity(ti, s, m)
    end

    skip_email = request.test_instance.skip_email.nil? ? false : request.test_instance.skip_email

    # ---  Create new test_instance --- #
    ti = TestInstance.new({
                            user_id: user.id,
                            test_profile_id: request.test_instance.test_profile_id,
                            test_language: request.test_instance.test_language,
                            test_mode: request.test_instance.test_mode,
                            redirection_url: request.test_instance.redirection_url,
                            # Client
                            client_type: request.source.client_type,
                            client_id: request.source.client_id,
                            client_name: request.source.client_name,
                            # Contact
                            client_contact_email: request.source.contact_email,
                            client_contact_first_name: request.source.contact_first_name,
                            client_contact_last_name: request.source.contact_last_name,
                            client_contact_locale: request.source.contact_locale,
                            # PClients
                            pipplet_clients_campaign_id: request.source.pipplet_clients_campaign_id,
                            skip_email: skip_email,
                            send_reminders: !skip_email && !request.test_instance.test_mode,
                            source_type: request.test_instance.source_type,
                            certificate_sent_to_candidate: request.test_instance.certificate_sent_to_candidate,
                            sms_reminders: request.test_instance.sms_reminders
                          })

    # Skip Identity check could be passed as argument or not. Let default value or set to false if it has been provided
    ti.skip_identity_check = false if request.test_instance.skip_identity_check == false

    ti.review_required = true if request.test_instance.review_required == true

    ti.direct_user_id = request.source.source_id if is_direct_user?(request) && request.source.source_id

    ti.save!

    unless is_direct_user?(request)
      ao = ApiOrder.find_by(id: request.source.source_id)
      ao&.update({ user_id: user.id, test_instance_id: ti.id })
      user.update!(identity_provider: ao.api_user.identity_provider) if ao&.api_user&.identity_provider
    end

    if is_request_api?(request) && ao
      ti.update(api_user_id: ao.api_user_id)
      # Check for RemoteClient rules
      remote_client_status = RemoteClient.match_and_apply(ti)
      CreatePippletClientsUserJob.perform_later(ti.id, ti.pipplet_clients_campaign_id) if remote_client_status
    end

    # Send email notification for candidates to take the test
    ti.send_email_notification_to_candidate!

    if is_direct_user?(request)
      # Trigger creation of user in pipplet-clients (async task)
      # by calling the job PippletClientsApi.create_user(test_instance_id, direct_user_id)
      du = DirectUser.order(:id).find_by(id: request.source.source_id)

      # Create ApiOrder for DirectUser

      if (api_user = ApiUser.find_by(name: 'main-dashboard')).present?
        du_api_order = ApiOrder.create({
                                         api_user_id: api_user.id,
                                         order_reference: du.id,
                                         order_information: { params: {} },
                                         status: 0,
                                         source: ApiOrder.sources[:dashboard],
                                         user_id: user.id,
                                         test_instance_id: ti.id
                                       })
        ti.update(api_user_id: du_api_order.api_user_id)
      end

      CreatePippletClientsUserJob.perform_later(ti.id, du.pipplet_clients_campaign_id) if du&.sync_with_pipplet_clients?

      # Send Email to candidate if candidate is B2C
      MailjetHelper.send_welcome_b2c_email(ti) if ti.test_profile.test_taker_type == 'b2c'
    end
    # --- EO: Post User & TestInstance creation callbacks --- #

    [user, ti]
  end

  def self.is_request_api?(request)
    request.source.source_type == :external_api
  end

  def self.is_direct_user?(request)
    request.source.source_type == :direct_user
  end

  def current_challenges_linguist_id_array
    challenges_array = productions.includes(:challenge).order('created_at').collect { |ti| ti.challenge.linguist_id }
    challenges_array += next_questionables.includes(:challenge).collect { |nq| nq.challenge.linguist_id }
    challenges_array.uniq
  end

  def current_challenges_id_array
    return @current_challenges_id_array if defined? @current_challenges_id_array

    @current_challenges_id_array = current_challenges_linguist_id_array.collect { |c| Challenge.find_by_linguist_id(c).id }
  end

  def get_full_challenge_id_array
    ## IF TI is not started, let's use the values from the test profile
    if not_started?
      full_challenge_id_array = test_profile.challenge_id_array
      full_challenge_id_array += test_profile.additional_challenge_id_array if test_profile.has_additional_translated_language?(test_language) && !test_profile.additional_linguist_ids.blank?
      full_challenge_id_array
    else
      ## If TI is started, let's use the values from the test instance's productions and next_questionables
      ## Return as an array of array to be coherent with the preceding part
      [current_challenges_id_array]
    end
  end

  def training?
    type_of_client?('training')
  end

  def recruitment?
    type_of_client?('recruitment')
  end

  def audio_productions_transcripted?
    audio_productions = productions.select(&:has_recording?)
    audio_productions.size == productions.select { |prod| !prod.production_metadatum&.production_transcription.nil? }.size
  end

  def talent_ai?
    @talent_ai ||= test_profile&.talent_ai? &&
                   Rails.application.config.ai.key?(test_language_sym) &&
                   client_config_enables_ai_assessments?
  end

  def qc_ai?
    @qc_ai ||= !talent_ai? &&
               Rails.application.config.ai.key?(test_language_sym) &&
               client_config_enables_ai_assessments?
  end

  def from_internal_api?
    @from_internal_api = source.to_sym.in?(ApiOrder::API_SOURCES.keys - [:external])
  end

  private

  def client_config_enables_ai_assessments?
    return true if client_config.nil?

    client_config.disable_ai_assessments == false
  end

  public

  def from_integration?
    @from_integration = source.to_sym.in? ApiOrder::INTEGRATION_SOURCES.keys
  end

  def reference_evaluation
    return nil unless sent_for_evaluation?

    evaluations.where.not(status: :canceled).order(created_at: :asc).first
  end

  def ai_workflow
    @ai_workflow ||= if talent_ai?
                       :talent_ai
                     elsif qc_ai?
                       :qc
                     end
  end

  def evaluations_workflow
    return :regrade if regrading?

    if reference_evaluation&.standard?
      :standard
    elsif reference_evaluation&.client_grade_check?
      :client_grade_check
    elsif reference_evaluation&.multiple_evaluations?
      :multiple_evaluations
    end
  end

  def ai_config
    return nil unless test_language && ai_workflow

    @ai_config ||= Rails.application.config.ai[test_language.to_sym][ai_workflow]
  end

  def assessment_type
    AssessmentType.find_for_test_instance(self)&.name
  end

  def ordered_completed_productions_for(type)
    return [] if ordered_challenge_ids.blank?

    ordered_challenge_ids.map do |c_id|
      productions.exclude_ignored.public_send(type).find_by(challenge_id: c_id)
    end.compact
  end

  def regrade!
    unless get_delivered_eval
      Alert.request_errors("[TestInstance Regrade] - Unable to regrade a test instance", "Delivered evaluation for test instance #{id} not found.", handled: true)
      return
    end

    send_for_evaluation! if update(regrade_requested_at: Time.current)
  end

  def anonymize_attributes!(force_associations: false, include_all_associations: true)
    skip_after_commit_when_attributes_change = true
    attributes = {}
    attributes[:client_contact_email] = Anonymized.email if client_contact_email.present?
    attributes[:client_contact_first_name] = Anonymized.email if client_contact_first_name.present?
    attributes[:client_contact_last_name] = Anonymized.email if client_contact_last_name.present?
    ActiveRecord::Base.connection.transaction do
      if include_all_associations && (user.test_instances.where('created_at > ?', Rails.application.config.anonymization.last_created_at).pluck(:id) - [id]).empty? && !user.anonymized?
        user.anonymize!(force: force_associations, include_all_associations: false)
      end
      update!(attributes) if attributes.any?
      api_order.anonymize!(force: force_associations) if api_order
      evaluations.each { |evaluation| evaluation.anonymize!(force: force_associations) }
      dynamic_question_data_from_productions.each { |dqd| dqd.anonymize!(force: force_associations) }
      images.destroy_all
      event_logs.destroy_all
    end

    report.clear
    raise ActiveRecord::RecordInvalid, self unless report.save
  end

  def anonymize_related!
    identity_objects.batch_delete!
  end

  def add_evaluation(**attributes)
    evaluation = Evaluation.new_for_test_instance(self)
    evaluation.assign_attributes(attributes)
    evaluation.save!
    evaluation
  end

  def add_and_assign_evaluation(**attributes)
    # TODO: move examiners_backup_order on TI
    attributes[:examiner] = evaluations.first.examiners_backup_order.sample || Examiner.find_admin_linguistic_examiner(test_language)
    evaluation = add_evaluation(**attributes)
    unless evaluation.can_be_assign_to_standard_examiner?
      evaluation.update(examiner: Examiner.find_admin_linguistic_examiner(test_language))
    end

    evaluation.assign!('assigned_at')
    evaluation
  end

  def add_and_force_assign_evaluation(**attributes)
    raise ArgumentError, 'Examiner must be provided' unless attributes[:examiner] && attributes[:examiner].is_a?(Examiner)

    evaluation = add_evaluation(**attributes)
    evaluation.force_assign!('assigned_at')
    evaluation
  end

  private

  def type_of_client?(client_source_type)
    source_type == client_source_type || test_profile.client_type == client_source_type
  end

  def log_status_change
    EventLog.status_change(self)
  end
end
