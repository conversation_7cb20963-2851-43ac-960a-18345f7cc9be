class WheeboxApi
  # Lightweight value object for the token response
  TokenResponse = Struct.new(:token, :expires)
  DEFAULT_TIMEOUT = 15 # seconds

  def initialize
    @base_url  = Rails.application.config.wheebox_proctoring[:base_url]
    @user_name = Rails.application.config.wheebox_proctoring[:user_name]
    @password  = Rails.application.config.wheebox_proctoring[:password]
  end

  def get_token
    url     = "#{@base_url}/getToken"
    payload = { userName: @user_name, password: @password }.to_json
    headers = { 'Content-Type' => 'application/json', 'Accept' => 'application/json' }

    response = HTTParty.post(url, body: payload, headers: headers, timeout: DEFAULT_TIMEOUT)

    unless response.success?
      error_message = "Failed to get token. Status: #{response.code}. Body: #{response.body}"
      raise WheeboxError, "Failed to get token (HTTP #{response.code})"
    end

    data = JSON.parse(response.body)
    TokenResponse.new(data['token'], data['expires'])
  rescue JSON::ParserError => e
    raise WheeboxError, "Invalid response format from Wheebox API when getting token."
  rescue HTTParty::Error => e
    raise WheeboxError, "API communication error when getting token: #{e.message}"
  rescue SocketError, Timeout::Error, Errno::ECONNREFUSED => e
    raise WheeboxError, "Network error when requesting token: #{e.message}"
  end

  # Registers a candidate for a Wheebox event
  def register_candidate(user_id, event_id, first_name, attempt_number, email)
    token = get_token.token # This might raise WheeboxError if get_token fails
    url     = "#{@base_url}/getRegistration"
    payload = {
      student_unique_id: user_id,
      event_id:,
      attemptnumber: attempt_number,
      student_name: if first_name,
      emailid: email,
      is100msEnbale: true
    }.to_json
    headers = {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Authorization' => token
    }

    response = HTTParty.post(url, body: payload, headers: headers, timeout: DEFAULT_TIMEOUT)

    unless response.success?
      raise WheeboxError, "Failed to register candidate (HTTP #{response.code})"
    end

    registration_data = JSON.parse(response.body)
    { data: registration_data, token: token }
  rescue JSON::ParserError => e
    raise WheeboxError, "Invalid response format from Wheebox API when registering candidate."
  rescue HTTParty::Error => e # Catches errors from HTTParty.post specifically
    raise WheeboxError, "API communication error when registering candidate: #{e.message}"
  rescue SocketError, Timeout::Error, Errno::ECONNREFUSED => e # Catches network errors from HTTParty.post
    raise WheeboxError, "Network error when registering candidate: #{e.message}"
    # WheeboxError from get_token will propagate up, no need to rescue it here unless specific handling is needed.
  end

  # Ends a proctoring session for a given attempt ID
  def end_test(attempt_id)
    token = get_token.token # This might raise WheeboxError if get_token fails
    url     = "#{@base_url}/endTestRpass"
    payload = {
      code: '1549000',
      student_unique_id: attempt_id
    }.to_json
    headers = {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Authorization' => token
    }

    response = HTTParty.post(url, body: payload, headers: headers, timeout: DEFAULT_TIMEOUT)

    unless response.success?
      raise WheeboxError, "Failed to end test (HTTP #{response.code})"
    end

    JSON.parse(response.body)
  rescue JSON::ParserError => e
    raise WheeboxError, "Invalid response format from Wheebox API when ending test."
  rescue HTTParty::Error => e # Catches errors from HTTParty.post specifically
    raise WheeboxError, "API communication error when ending test: #{e.message}"
  rescue SocketError, Timeout::Error, Errno::ECONNREFUSED => e # Catches network errors from HTTParty.post
    raise WheeboxError, "Network error when ending test: #{e.message}"
    # WheeboxError from get_token will propagate up.
  end

  # Generates a page URL for face training
  def generate_page_url(user_id, return_url, event_id, first_name, last_name)
    token = get_token.token # This might raise WheeboxError if get_token fails
    url = "https://wgc.wheebox.com/WheeboxRestService/generatepageURL"
    payload = {
      student_unique_id: user_id,
      custom_logo: '',
      custom_title: '',
      colortheam: '#1f1f19',
      return_url: return_url,
      event_id: event_id,
      param: '',
      pagetype: 'train',
      autoapproval: true,
      attemptnumber: 1,
      captureimage: 'both',
      dob: '2000-01-01',
      father_name: last_name,
      fullname: "#{last_name}, #{first_name}"
    }.to_json
    headers = {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Authorization' => token
    }

    response = HTTParty.post(url, body: payload, headers: headers, timeout: DEFAULT_TIMEOUT)

    unless response.success?
      raise WheeboxError, "Failed to generate page URL (HTTP #{response.code})"
    end

    data = JSON.parse(response.body)
    {
      attempt_id: data['attemptId'],
      attempt_url: data['attemptUrl'],
      assessment_id: data['assessmentId'],
      token: token
    }
  rescue JSON::ParserError => e
    raise WheeboxError, "Invalid response format from Wheebox API when generating page URL."
  rescue HTTParty::Error => e
    raise WheeboxError, "API communication error when generating page URL: #{e.message}"
  rescue SocketError, Timeout::Error, Errno::ECONNREFUSED => e
    raise WheeboxError, "Network error when generating page URL: #{e.message}"
  end

  class WheeboxError < StandardError; end
end
