# Not enabled by default because gives wrong data when using parallel tests
if ENV['COVERAGE'] == 'true'
  require 'simplecov'
  SimpleCov.start do
    add_filter '/test/'
    add_filter '/bin/'
    add_filter '/config/'
    add_filter '/db/'
    add_filter '/doc/'
    add_filter '/log/'
    add_filter '/spec/'
    add_filter '/test/'
    add_filter '/vendor/'
    add_group 'Models', 'app/models'
    add_group 'Controllers', 'app/controllers'
    add_group 'Helpers', 'app/helpers'
    add_group 'Jobs', 'app/jobs'
    add_group 'Mailers', 'app/mailers'
    add_group 'Serializers', 'app/serializers'
    add_group 'Lib', 'lib'
    add_group 'Services', 'app/services'
  end
end

require File.expand_path('../config/environment', __dir__)
# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?
# Add additional requires below this line. Rails is not loaded until this point!
require Rails.root.join('spec/support/webmock')
require 'rails/test_help'
require 'ostruct'
require 'minitest'
require 'minitest/reporters'

Minitest::Reporters.use! unless ENV['RM_INFO']
# Exchange for this if you want to see duration for each test
# Minitest::Reporters.use! Minitest::Reporters::SpecReporter.new

require 'webmock/minitest'
require 'mocha/minitest'
require 'capybara/rails'
require 'capybara-lockstep'
require 'aws-sdk-s3'

Aws.config[:s3] = { stub_responses: true }

# Requires supporting ruby files with custom matchers and macros, etc, in
# test/support/ and its subdirectories. Files matching `test/**/*_test.rb` are
# run as test files by default. This means that files in test/support that end
# in _test.rb will both be required and run as tests, causing the tests to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _test.rb. You can configure this pattern with the --pattern
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_test.rb` files, manually
# require only the support files necessary.
#
Dir[Rails.root.join('test', 'support', '**', '*.rb')].sort.each { |f| require f }

include Mocha::API
Mocha.mocha_setup

class ActiveSupport::TestCase
  include FactoryBot::Syntax::Methods
  include Devise::Test::IntegrationHelpers
  include SessionsTestHelper
  include EmailsTestHelper
  include EventLogsTestHelper

  EmailsTestHelper.register_validate_email_domain_stub

  class << self
    attr_accessor :skipped_in_ci

    def skip_in_ci
      self.skipped_in_ci = true
    end
  end

  def before_setup
    skip("Test #{self.method_name} from a class #{self.class} set not to run in CI") if ENV.fetch('CI', false) && self.class.skipped_in_ci
    Bullet.start_request if Rails.application.config.test_with[:bullet]
    Prosopite.scan if Rails.application.config.test_with[:prosopite]
    ActiveSupport::TestCase.register_global_stubbed_requests
    super
  end

  def after_teardown
    super
    if Rails.application.config.test_with[:bullet]
      Bullet.perform_out_of_channel_notifications if Bullet.notification?
      Bullet.end_request
    end
    Prosopite.finish if Rails.application.config.test_with[:prosopite]
  end

  Rails.application.config.raise_on_alert_creation = false

  if ENV['COVERAGE'] == 'true' # system tests break with parallelization and only one browser
    puts 'Running tests with coverage and without parallelization'
    WebMock::API.stub_request(:any, %r{\A#{PippletClientsApi.api_url}/api/v1/.*\z}).to_return(status: 200, body: '')

    Rails.application.load_seed
    ActiveRecord::Base.connection.reset_pk_sequence!('test_profiles')
    ActiveRecord::Base.connection.reset_pk_sequence!('assessment_questions')
  else
    puts "#{Concurrent.processor_count} cores available"
    number_of_workers = [2, (Concurrent.processor_count / 2).round].max
    puts "Running tests with #{number_of_workers} processes (+ chromedriver)"
    parallelize_setup do
      register_global_stubbed_requests # for seeds
      Rails.application.load_seed
      ActiveRecord::Base.connection.reset_pk_sequence!('test_profiles')
      ActiveRecord::Base.connection.reset_pk_sequence!('assessment_questions')
    end
    parallelize(workers: number_of_workers, threshold: 0)
  end

  def self.register_global_stubbed_requests
    WebMock::API.stub_request(:any, %r{\A#{PippletClientsApi.api_url}/api/v1/.*\z}).to_return(status: 200, body: '')
    WebMock::API.stub_request(:post, /\A#{Mailjet.config.end_point}.*\z/).to_return(body: {
      "Sent" => [
        { 'Email' => '<EMAIL>', 'MessageID' => 111111111111111 }
      ]
    }.to_json)
    WebMock::API.stub_request(:post, %r{\Ahttps://docs.google.com/forms/d/e/.*\z}).to_return(status: 200, body: "", headers: {})
    WebMock::API.stub_request(:post, %r{\Ahttps://script.google.com.*\z}).to_return(status: 200)
  end
end

class ActionDispatch::SystemTestCase
  # Selenium requests interceptor
  include Integrations::RequestsInterceptorTestHelper

  # Make the Capybara DSL available in all integration tests
  include Capybara::DSL
  # Make `assert_*` methods behave like Minitest assertions
  include Capybara::Minitest::Assertions

  # Devise sign_in sign_up helpers
  include Devise::Test::IntegrationHelpers

  # Overrides methods from SessionsTestHelper
  include Integrations::SessionsTestHelper

  Rails.application.config.raise_on_alert_creation = false

  Capybara.register_driver :headless_chrome do |app|
    options = Selenium::WebDriver::Chrome::Options.new
    options.add_argument('disable-dev-shm-usage')
    options.add_argument('no-sandbox')
    options.add_argument('disable-gpu')
    options.add_argument('headless=new')
    options.add_argument('window-size=1400,1000')
    options.add_preference(:intl, accept_languages: 'en-US')

    # Disable the 2023+ search engine choice screen
    options.add_argument('disable-search-engine-choice-screen')
    # Bypass the media stream infobar by selecting the default device for media streams (e.g. WebRTC).
    options.add_argument('use-fake-device-for-media-stream')
    # Normally, Chrome will treat a 'foreground' tab instead as backgrounded if the surrounding window is occluded (aka visually covered) by another window. This flag disables that.
    options.add_argument('disable-backgrounding-occluded-windows')
    # This disables non-foreground tabs from getting a lower process priority. Useful for parallel test runs.
    options.add_argument('disable-renderer-backgrounding')

    options.add_preference('profile.managed_default_content_settings.media_stream', 1)
    options.add_option('goog:loggingPrefs', { browser: 'ALL' })

    # capybara-lockstep: We recommend you configure your webdriver to not automatically dismiss user prompts by setting the "unhandled prompt behavior" capability to ignore. Using "ignore", errors are raised like with the default behavior, but user prompts are kept open.
    options.add_option(:unhandled_prompt_behavior, 'ignore')
    # Create a custom HTTP client with an increased timeout
    http_client = Selenium::WebDriver::Remote::Http::Default.new
    http_client.open_timeout = 120
    http_client.read_timeout = 120

    Capybara::Selenium::Driver.new(app, browser: :chrome, options:, http_client:)
  end

  driven_by :headless_chrome

  WebMock.disable_net_connect!(allow_localhost: true, allow: %w[chromedriver.storage.googleapis.com chrome-server])

  Capybara.server = :puma, { Silent: true, Threads: '1:2' }
  Capybara.default_max_wait_time = 5
  Capybara::Lockstep.timeout = 5
end
